diff --git a/android/src/main/java/com/brentvatne/common/react/VideoEventEmitter.kt b/android/src/main/java/com/brentvatne/common/react/VideoEventEmitter.kt
index 02b2922643aa9c6f831ba8309206016d4b9b9bcf..13fd02dd9631ff61807f7219a6c11e4634ed83f1 100644
--- a/android/src/main/java/com/brentvatne/common/react/VideoEventEmitter.kt
+++ b/android/src/main/java/com/brentvatne/common/react/VideoEventEmitter.kt
@@ -288,12 +288,17 @@ class VideoEventEmitter {
         }
     }
 
+    private class VideoCustomEvent(surfaceId: Int, viewId: Int, private val event: EventTypes, private val paramsSetter: (WritableMap.() -> Unit)?) :
+        Event<VideoCustomEvent>(surfaceId, viewId) {
+
+        override fun getEventName(): String = "top${event.eventName.removePrefix("on")}"
+
+        override fun getEventData(): WritableMap? = Arguments.createMap().apply(paramsSetter ?: {})
+    }
+
     private class EventBuilder(private val surfaceId: Int, private val viewId: Int, private val dispatcher: EventDispatcher) {
         fun dispatch(event: EventTypes, paramsSetter: (WritableMap.() -> Unit)? = null) =
-            dispatcher.dispatchEvent(object : Event<Event<*>>(surfaceId, viewId) {
-                override fun getEventName() = "top${event.eventName.removePrefix("on")}"
-                override fun getEventData() = Arguments.createMap().apply(paramsSetter ?: {})
-            })
+            dispatcher.dispatchEvent(VideoCustomEvent(surfaceId, viewId, event, paramsSetter))
     }
 
     private fun audioTracksToArray(audioTracks: java.util.ArrayList<Track>?): WritableArray =
