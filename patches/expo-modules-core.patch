diff --git a/android/src/main/java/com/facebook/react/uimanager/ReactStylesDiffMapHelper.kt b/android/src/main/java/com/facebook/react/uimanager/ReactStylesDiffMapHelper.kt
index b659e3c403f437a7db5ee6bc05fb00ca0009efdc..b2976382094995cffbbc96494e4049ed2e71bcf5 100644
--- a/android/src/main/java/com/facebook/react/uimanager/ReactStylesDiffMapHelper.kt
+++ b/android/src/main/java/com/facebook/react/uimanager/ReactStylesDiffMapHelper.kt
@@ -1,10 +1,25 @@
 package com.facebook.react.uimanager
 
 import com.facebook.react.bridge.ReadableMap
+import expo.modules.core.interfaces.DoNotStrip
+import java.lang.reflect.Field
+
+@get:DoNotStrip
+private val backingMapField: Field by lazy {
+  ReactStylesDiffMap::class.java.getDeclaredField("backingMap").apply {
+    isAccessible = true
+  }
+}
 
 /**
  * Access the package private property declared inside of [ReactStylesDiffMap]
+ * TODO: We should stop using this field and find a better way to access the backing map:
+ * See: https://github.com/facebook/react-native/pull/51386
  */
 fun ReactStylesDiffMap.getBackingMap(): ReadableMap {
-  return mBackingMap
+  return try {
+    backingMapField.get(this) as ReadableMap
+  } catch (e: ReflectiveOperationException) {
+    throw RuntimeException("Unable to access internal_backingMap via reflection", e)
+  }
 }
\ No newline at end of file
diff --git a/android/src/main/java/expo/modules/kotlin/ReactLifecycleDelegate.kt b/android/src/main/java/expo/modules/kotlin/ReactLifecycleDelegate.kt
index f4da1dfd535f9f8106ba0952a29bfa2f58279a8b..f37752d744d788699233505519e9a7bfd35cbdd5 100644
--- a/android/src/main/java/expo/modules/kotlin/ReactLifecycleDelegate.kt
+++ b/android/src/main/java/expo/modules/kotlin/ReactLifecycleDelegate.kt
@@ -37,7 +37,7 @@ class ReactLifecycleDelegate(appContext: AppContext) : LifecycleEventListener, A
     appContextHolder.get()?.onActivityResult(activity, requestCode, resultCode, data)
   }
 
-  override fun onNewIntent(intent: Intent?) {
+  override fun onNewIntent(intent: Intent) {
     appContextHolder.get()?.onNewIntent(intent)
   }
 }
\ No newline at end of file
diff --git a/android/src/main/java/expo/modules/kotlin/events/KModuleEventEmitterWrapper.kt b/android/src/main/java/expo/modules/kotlin/events/KModuleEventEmitterWrapper.kt
index 7440f2b9596bfccf3585f3454596b0586f0f6f21..4803df3fbae5595cafd6824b6ee92ee85fb59202 100644
--- a/android/src/main/java/expo/modules/kotlin/events/KModuleEventEmitterWrapper.kt
+++ b/android/src/main/java/expo/modules/kotlin/events/KModuleEventEmitterWrapper.kt
@@ -110,11 +110,11 @@ open class KEventEmitterWrapper(
   private class UIEvent(
     surfaceId: Int,
     viewId: Int,
-    private val eventName: String,
+    private val eventNameInternal: String,
     private val eventBody: WritableMap?,
     private val coalescingKey: Short?
   ) : com.facebook.react.uimanager.events.Event<UIEvent>(surfaceId, viewId) {
-    override fun getEventName(): String = normalizeEventName(eventName)
+    override fun getEventName(): String = normalizeEventName(eventNameInternal)
     override fun canCoalesce(): Boolean = coalescingKey != null
     override fun getCoalescingKey(): Short = coalescingKey ?: 0
     override fun getEventData(): WritableMap = eventBody ?: Arguments.createMap()
diff --git a/android/src/main/java/expo/modules/kotlin/exception/CodedException.kt b/android/src/main/java/expo/modules/kotlin/exception/CodedException.kt
index d1ed54b43d6acabbc6d15c3354aeaf95d8d776ba..24ae6a4ba7f4c2ced3be2b63b3df356f0a90e5a0 100644
--- a/android/src/main/java/expo/modules/kotlin/exception/CodedException.kt
+++ b/android/src/main/java/expo/modules/kotlin/exception/CodedException.kt
@@ -247,6 +247,13 @@ internal class CollectionElementCastException private constructor(
   ) : this(collectionType, elementType, providedType.toString(), cause)
 }
 
+@DoNotStrip
+class DynamicCastException(
+  type: KClass<*>
+) : CodedException(
+  message = "Could not cast dynamic value to '${type.qualifiedName}'."
+)
+
 @DoNotStrip
 class JavaScriptEvaluateException(
   message: String,
diff --git a/android/src/main/java/expo/modules/kotlin/records/RecordTypeConverter.kt b/android/src/main/java/expo/modules/kotlin/records/RecordTypeConverter.kt
index c29aa24e280a30fcba9b51322974e1bda4d3e322..52ef054dbe2aca127a058f2285924c5a24bae2c6 100644
--- a/android/src/main/java/expo/modules/kotlin/records/RecordTypeConverter.kt
+++ b/android/src/main/java/expo/modules/kotlin/records/RecordTypeConverter.kt
@@ -1,10 +1,13 @@
 package expo.modules.kotlin.records
 
 import com.facebook.react.bridge.Dynamic
+import com.facebook.react.bridge.ReadableArray
 import com.facebook.react.bridge.ReadableMap
 import expo.modules.kotlin.AppContext
 import expo.modules.kotlin.allocators.ObjectConstructor
 import expo.modules.kotlin.allocators.ObjectConstructorFactory
+import expo.modules.kotlin.exception.CodedException
+import expo.modules.kotlin.exception.DynamicCastException
 import expo.modules.kotlin.exception.FieldCastException
 import expo.modules.kotlin.exception.FieldRequiredException
 import expo.modules.kotlin.exception.RecordCastException
@@ -48,7 +51,7 @@ class RecordTypeConverter<T : Record>(
 
   override fun convertFromDynamic(value: Dynamic, context: AppContext?): T =
     exceptionDecorator({ cause -> RecordCastException(type, cause) }) {
-      val jsMap = value.asMap()
+      val jsMap = value.asMap() ?: throw DynamicCastException(ReadableMap::class)
       return convertFromReadableMap(jsMap, context)
     }
 
diff --git a/android/src/main/java/expo/modules/kotlin/types/AnyTypeConverter.kt b/android/src/main/java/expo/modules/kotlin/types/AnyTypeConverter.kt
index 68a66279ad0f36db1df25c97757d297b966c99e0..23140b569af72f47b4586a888c8b1870be88f15f 100644
--- a/android/src/main/java/expo/modules/kotlin/types/AnyTypeConverter.kt
+++ b/android/src/main/java/expo/modules/kotlin/types/AnyTypeConverter.kt
@@ -1,8 +1,12 @@
 package expo.modules.kotlin.types
 
 import com.facebook.react.bridge.Dynamic
+import com.facebook.react.bridge.ReadableArray
+import com.facebook.react.bridge.ReadableMap
 import com.facebook.react.bridge.ReadableType
 import expo.modules.kotlin.AppContext
+import expo.modules.kotlin.exception.NullArgumentException
+import expo.modules.kotlin.exception.DynamicCastException
 import expo.modules.kotlin.jni.CppType
 import expo.modules.kotlin.jni.ExpectedType
 
@@ -17,10 +21,10 @@ class AnyTypeConverter(isOptional: Boolean) : DynamicAwareTypeConverters<Any>(is
     return when (value.type) {
       ReadableType.Boolean -> value.asBoolean()
       ReadableType.Number -> value.asDouble()
-      ReadableType.String -> value.asString()
-      ReadableType.Map -> value.asMap().toHashMap()
-      ReadableType.Array -> value.asArray().toArrayList()
-      else -> error("Unknown dynamic type: ${value.type}")
+      ReadableType.String -> value.asString() ?: throw DynamicCastException(String::class)
+      ReadableType.Map -> (value.asMap()  ?: throw DynamicCastException(ReadableMap::class)).toHashMap()
+      ReadableType.Array -> (value.asArray()  ?: throw DynamicCastException(ReadableArray::class)).toArrayList()
+      ReadableType.Null -> throw NullArgumentException()
     }
   }
 
diff --git a/android/src/main/java/expo/modules/kotlin/types/ArrayTypeConverter.kt b/android/src/main/java/expo/modules/kotlin/types/ArrayTypeConverter.kt
index ef7497970181c0fd9e4558ed2dd0aa59b0eed6ce..56623d213db2b9a217917a02e61ff325f8923ede 100644
--- a/android/src/main/java/expo/modules/kotlin/types/ArrayTypeConverter.kt
+++ b/android/src/main/java/expo/modules/kotlin/types/ArrayTypeConverter.kt
@@ -1,8 +1,10 @@
 package expo.modules.kotlin.types
 
 import com.facebook.react.bridge.Dynamic
+import com.facebook.react.bridge.ReadableArray
 import expo.modules.kotlin.AppContext
 import expo.modules.kotlin.exception.CollectionElementCastException
+import expo.modules.kotlin.exception.DynamicCastException
 import expo.modules.kotlin.exception.exceptionDecorator
 import expo.modules.kotlin.jni.ExpectedType
 import expo.modules.kotlin.recycle
@@ -20,7 +22,7 @@ class ArrayTypeConverter(
   )
 
   override fun convertFromDynamic(value: Dynamic, context: AppContext?): Array<*> {
-    val jsArray = value.asArray()
+    val jsArray = value.asArray() ?: throw DynamicCastException(ReadableArray::class)
     val array = createTypedArray(jsArray.size())
     for (i in 0 until jsArray.size()) {
       array[i] = jsArray
diff --git a/android/src/main/java/expo/modules/kotlin/types/ColorTypeConverter.kt b/android/src/main/java/expo/modules/kotlin/types/ColorTypeConverter.kt
index 136cc5487ed563d7096956f751a93ea93228c537..cf4825b7c098b20f97472abf78e2ae68400e0ab2 100644
--- a/android/src/main/java/expo/modules/kotlin/types/ColorTypeConverter.kt
+++ b/android/src/main/java/expo/modules/kotlin/types/ColorTypeConverter.kt
@@ -6,10 +6,13 @@ import androidx.annotation.RequiresApi
 import com.facebook.react.bridge.Dynamic
 import com.facebook.react.bridge.ReadableType
 import expo.modules.kotlin.AppContext
+import expo.modules.kotlin.exception.DynamicCastException
 import expo.modules.kotlin.exception.UnexpectedException
 import expo.modules.kotlin.jni.CppType
 import expo.modules.kotlin.jni.ExpectedType
 import expo.modules.kotlin.jni.SingleType
+import androidx.core.graphics.toColorInt
+import com.facebook.react.bridge.ReadableArray
 
 /**
  * Color components for named colors following the [CSS3/SVG specification](https://www.w3.org/TR/css-color-3/#svg-color)
@@ -176,9 +179,9 @@ class ColorTypeConverter(
   override fun convertFromDynamic(value: Dynamic, context: AppContext?): Color {
     return when (value.type) {
       ReadableType.Number -> colorFromInt(value.asDouble().toInt())
-      ReadableType.String -> colorFromString(value.asString())
+      ReadableType.String -> colorFromString(value.asString() ?: throw DynamicCastException(String::class))
       ReadableType.Array -> {
-        val colorsArray = value.asArray().toArrayList().map { it as Double }.toDoubleArray()
+        val colorsArray = (value.asArray() ?: throw DynamicCastException(ReadableArray::class)).toArrayList().map { it as Double }.toDoubleArray()
         colorFromDoubleArray(colorsArray)
       }
       else -> throw UnexpectedException("Unknown argument type: ${value.type}")
diff --git a/android/src/main/java/expo/modules/kotlin/types/EnumTypeConverter.kt b/android/src/main/java/expo/modules/kotlin/types/EnumTypeConverter.kt
index 7e3f177b068638ce818e009e4aae5865e6373b17..5ff68174038a93c391d0419812db97434a5e9d5e 100644
--- a/android/src/main/java/expo/modules/kotlin/types/EnumTypeConverter.kt
+++ b/android/src/main/java/expo/modules/kotlin/types/EnumTypeConverter.kt
@@ -2,6 +2,7 @@ package expo.modules.kotlin.types
 
 import com.facebook.react.bridge.Dynamic
 import expo.modules.kotlin.AppContext
+import expo.modules.kotlin.exception.DynamicCastException
 import expo.modules.kotlin.exception.EnumNoSuchValueException
 import expo.modules.kotlin.exception.IncompatibleArgTypeException
 import expo.modules.kotlin.jni.ExpectedType
@@ -39,7 +40,7 @@ class EnumTypeConverter(
 
   override fun convertFromDynamic(value: Dynamic, context: AppContext?): Enum<*> {
     if (primaryConstructor.parameters.isEmpty()) {
-      return convertEnumWithoutParameter(value.asString(), enumConstants)
+      return convertEnumWithoutParameter(value.asString() ?: throw DynamicCastException(String::class), enumConstants)
     } else if (primaryConstructor.parameters.size == 1) {
       return convertEnumWithParameter(
         value,
diff --git a/android/src/main/java/expo/modules/kotlin/types/ExpoDynamic.kt b/android/src/main/java/expo/modules/kotlin/types/ExpoDynamic.kt
index 5e281970bea5779eaa78954dac95ec80a92ee43f..f90952060e13a5bd928b642fe4e2a9e4fc9175bf 100644
--- a/android/src/main/java/expo/modules/kotlin/types/ExpoDynamic.kt
+++ b/android/src/main/java/expo/modules/kotlin/types/ExpoDynamic.kt
@@ -1,6 +1,9 @@
 package expo.modules.kotlin.types
 
 import com.facebook.react.bridge.Dynamic
+import com.facebook.react.bridge.ReadableArray
+import com.facebook.react.bridge.ReadableMap
+import expo.modules.kotlin.exception.DynamicCastException
 
 class ExpoDynamic(private val dynamic: Dynamic) {
   enum class Type {
@@ -32,7 +35,7 @@ class ExpoDynamic(private val dynamic: Dynamic) {
     }
 
   fun asArray(): List<Any?> {
-    return dynamic.asArray().toArrayList()
+    return (dynamic.asArray() ?: throw DynamicCastException(ReadableArray::class)).toArrayList()
   }
 
   fun asBoolean(): Boolean {
@@ -48,10 +51,10 @@ class ExpoDynamic(private val dynamic: Dynamic) {
   }
 
   fun asMap(): Map<String, Any?> {
-    return dynamic.asMap().toHashMap()
+    return (dynamic.asMap() ?: throw DynamicCastException(ReadableMap::class)).toHashMap()
   }
 
   fun asString(): String {
-    return dynamic.asString()
+    return dynamic.asString() ?: throw DynamicCastException(String::class)
   }
 }
diff --git a/android/src/main/java/expo/modules/kotlin/types/ListTypeConverter.kt b/android/src/main/java/expo/modules/kotlin/types/ListTypeConverter.kt
index 000c2d51d1b5802873d25483f374300bab067969..af151239a526eced319ec141ff31d8261811a982 100644
--- a/android/src/main/java/expo/modules/kotlin/types/ListTypeConverter.kt
+++ b/android/src/main/java/expo/modules/kotlin/types/ListTypeConverter.kt
@@ -5,6 +5,7 @@ import com.facebook.react.bridge.ReadableArray
 import com.facebook.react.bridge.ReadableType
 import expo.modules.kotlin.AppContext
 import expo.modules.kotlin.exception.CollectionElementCastException
+import expo.modules.kotlin.exception.DynamicCastException
 import expo.modules.kotlin.exception.exceptionDecorator
 import expo.modules.kotlin.jni.ExpectedType
 import expo.modules.kotlin.recycle
@@ -36,7 +37,7 @@ class ListTypeConverter(
       )
     }
 
-    val jsArray = value.asArray()
+    val jsArray = value.asArray() ?: throw DynamicCastException(ReadableArray::class)
     return convertFromReadableArray(jsArray, context)
   }
 
diff --git a/android/src/main/java/expo/modules/kotlin/types/MapTypeConverter.kt b/android/src/main/java/expo/modules/kotlin/types/MapTypeConverter.kt
index adb75c4f8edbe8afb89b7e441e3445c6fe00f1bd..b7e0905c88f452928d1022c02b414a51e3d19f95 100644
--- a/android/src/main/java/expo/modules/kotlin/types/MapTypeConverter.kt
+++ b/android/src/main/java/expo/modules/kotlin/types/MapTypeConverter.kt
@@ -5,6 +5,7 @@ import com.facebook.react.bridge.DynamicFromObject
 import com.facebook.react.bridge.ReadableMap
 import expo.modules.kotlin.AppContext
 import expo.modules.kotlin.exception.CollectionElementCastException
+import expo.modules.kotlin.exception.DynamicCastException
 import expo.modules.kotlin.exception.exceptionDecorator
 import expo.modules.kotlin.jni.ExpectedType
 import expo.modules.kotlin.recycle
@@ -27,7 +28,7 @@ class MapTypeConverter(
   )
 
   override fun convertFromDynamic(value: Dynamic, context: AppContext?): Map<*, *> {
-    val jsMap = value.asMap()
+    val jsMap = value.asMap() ?: throw DynamicCastException(ReadableMap::class)
     return convertFromReadableMap(jsMap, context)
   }
 
diff --git a/android/src/main/java/expo/modules/kotlin/types/PairTypeConverter.kt b/android/src/main/java/expo/modules/kotlin/types/PairTypeConverter.kt
index 0278e679481244cc87c8f1ff303a22c115e4536a..eda4b59dd12634c1512392fd4bfc20175bea26a8 100644
--- a/android/src/main/java/expo/modules/kotlin/types/PairTypeConverter.kt
+++ b/android/src/main/java/expo/modules/kotlin/types/PairTypeConverter.kt
@@ -4,6 +4,7 @@ import com.facebook.react.bridge.Dynamic
 import com.facebook.react.bridge.ReadableArray
 import expo.modules.kotlin.AppContext
 import expo.modules.kotlin.exception.CollectionElementCastException
+import expo.modules.kotlin.exception.DynamicCastException
 import expo.modules.kotlin.exception.exceptionDecorator
 import expo.modules.kotlin.jni.CppType
 import expo.modules.kotlin.jni.ExpectedType
@@ -29,7 +30,7 @@ class PairTypeConverter(
   )
 
   override fun convertFromDynamic(value: Dynamic, context: AppContext?): Pair<*, *> {
-    val jsArray = value.asArray()
+    val jsArray = value.asArray() ?: throw DynamicCastException(ReadableArray::class)
     return convertFromReadableArray(jsArray, context)
   }
 
diff --git a/android/src/main/java/expo/modules/kotlin/types/ReadableArgumentsTypeConverter.kt b/android/src/main/java/expo/modules/kotlin/types/ReadableArgumentsTypeConverter.kt
index ae6235bcbc0648a8d8590cdb79248247682aa318..d4c1834f8f608d21731ce59226d29873e9190397 100644
--- a/android/src/main/java/expo/modules/kotlin/types/ReadableArgumentsTypeConverter.kt
+++ b/android/src/main/java/expo/modules/kotlin/types/ReadableArgumentsTypeConverter.kt
@@ -5,6 +5,7 @@ import com.facebook.react.bridge.ReadableMap
 import expo.modules.core.arguments.MapArguments
 import expo.modules.core.arguments.ReadableArguments
 import expo.modules.kotlin.AppContext
+import expo.modules.kotlin.exception.DynamicCastException
 import expo.modules.kotlin.jni.CppType
 import expo.modules.kotlin.jni.ExpectedType
 
@@ -12,7 +13,7 @@ class ReadableArgumentsTypeConverter(
   isOptional: Boolean
 ) : DynamicAwareTypeConverters<ReadableArguments>(isOptional) {
   override fun convertFromDynamic(value: Dynamic, context: AppContext?): ReadableArguments {
-    return MapArguments(value.asMap().toHashMap())
+    return MapArguments((value.asMap() ?: throw DynamicCastException(ReadableMap::class)).toHashMap())
   }
 
   override fun convertFromAny(value: Any, context: AppContext?): ReadableArguments {
diff --git a/android/src/main/java/expo/modules/kotlin/types/SetTypeConverter.kt b/android/src/main/java/expo/modules/kotlin/types/SetTypeConverter.kt
index bcc80c082eb0033f7e53781abb2713f32dfbb6e9..8319781d283e229192e01544541592d74339de0c 100644
--- a/android/src/main/java/expo/modules/kotlin/types/SetTypeConverter.kt
+++ b/android/src/main/java/expo/modules/kotlin/types/SetTypeConverter.kt
@@ -4,6 +4,7 @@ import com.facebook.react.bridge.Dynamic
 import com.facebook.react.bridge.ReadableArray
 import expo.modules.kotlin.AppContext
 import expo.modules.kotlin.exception.CollectionElementCastException
+import expo.modules.kotlin.exception.DynamicCastException
 import expo.modules.kotlin.exception.exceptionDecorator
 import expo.modules.kotlin.jni.ExpectedType
 import expo.modules.kotlin.recycle
@@ -20,7 +21,7 @@ class SetTypeConverter(
   )
 
   override fun convertFromDynamic(value: Dynamic, context: AppContext?): Set<*> {
-    val jsArray = value.asArray()
+    val jsArray = value.asArray() ?: throw DynamicCastException(ReadableArray::class)
     return convertFromReadableArray(jsArray, context)
   }
 
diff --git a/android/src/main/java/expo/modules/kotlin/types/TypeConverterProvider.kt b/android/src/main/java/expo/modules/kotlin/types/TypeConverterProvider.kt
index 18496b73dd77b3fc5b7c93eac320408e1ae45c26..5c5c4dc5fcbb58fddfb64254bc0e6bb13b73e644 100644
--- a/android/src/main/java/expo/modules/kotlin/types/TypeConverterProvider.kt
+++ b/android/src/main/java/expo/modules/kotlin/types/TypeConverterProvider.kt
@@ -8,6 +8,7 @@ import com.facebook.react.bridge.ReadableArray
 import com.facebook.react.bridge.ReadableMap
 import expo.modules.core.arguments.ReadableArguments
 import expo.modules.kotlin.apifeatures.EitherType
+import expo.modules.kotlin.exception.DynamicCastException
 import expo.modules.kotlin.exception.MissingTypeConverter
 import expo.modules.kotlin.jni.CppType
 import expo.modules.kotlin.jni.ExpectedType
@@ -205,19 +206,19 @@ object TypeConverterProviderImpl : TypeConverterProvider {
 
       String::class to createTrivialTypeConverter(
         isOptional, ExpectedType(CppType.STRING)
-      ) { it.asString() },
+      ) { it.asString() ?: throw DynamicCastException(String::class) },
 
       ReadableArray::class to createTrivialTypeConverter(
         isOptional, ExpectedType(CppType.READABLE_ARRAY)
-      ) { it.asArray() },
+      ) { it.asArray() ?: throw DynamicCastException(ReadableArray::class) },
       ReadableMap::class to createTrivialTypeConverter(
         isOptional, ExpectedType(CppType.READABLE_MAP)
-      ) { it.asMap() },
+      ) { it.asMap() ?: throw DynamicCastException(ReadableMap::class) },
 
       IntArray::class to createTrivialTypeConverter(
         isOptional, ExpectedType.forPrimitiveArray(CppType.INT)
       ) {
-        val jsArray = it.asArray()
+        val jsArray = it.asArray() ?: throw DynamicCastException(ReadableArray::class)
         IntArray(jsArray.size()) { index ->
           jsArray.getInt(index)
         }
@@ -225,7 +226,7 @@ object TypeConverterProviderImpl : TypeConverterProvider {
       LongArray::class to createTrivialTypeConverter(
         isOptional, ExpectedType.forPrimitiveArray(CppType.LONG)
       ) {
-        val jsArray = it.asArray()
+        val jsArray = it.asArray() ?: throw DynamicCastException(ReadableArray::class)
         LongArray(jsArray.size()) { index ->
           jsArray.getDouble(index).toLong()
         }
@@ -233,7 +234,7 @@ object TypeConverterProviderImpl : TypeConverterProvider {
       DoubleArray::class to createTrivialTypeConverter(
         isOptional, ExpectedType.forPrimitiveArray(CppType.DOUBLE)
       ) {
-        val jsArray = it.asArray()
+        val jsArray = it.asArray() ?: throw DynamicCastException(ReadableArray::class)
         DoubleArray(jsArray.size()) { index ->
           jsArray.getDouble(index)
         }
@@ -241,7 +242,7 @@ object TypeConverterProviderImpl : TypeConverterProvider {
       FloatArray::class to createTrivialTypeConverter(
         isOptional, ExpectedType.forPrimitiveArray(CppType.FLOAT)
       ) {
-        val jsArray = it.asArray()
+        val jsArray = it.asArray() ?: throw DynamicCastException(ReadableArray::class)
         FloatArray(jsArray.size()) { index ->
           jsArray.getDouble(index).toFloat()
         }
@@ -249,7 +250,7 @@ object TypeConverterProviderImpl : TypeConverterProvider {
       BooleanArray::class to createTrivialTypeConverter(
         isOptional, ExpectedType.forPrimitiveArray(CppType.BOOLEAN)
       ) {
-        val jsArray = it.asArray()
+        val jsArray = it.asArray() ?: throw DynamicCastException(ReadableArray::class)
         BooleanArray(jsArray.size()) { index ->
           jsArray.getBoolean(index)
         }
diff --git a/android/src/main/java/expo/modules/kotlin/types/net/UriTypeConverter.kt b/android/src/main/java/expo/modules/kotlin/types/net/UriTypeConverter.kt
index f9ce4547e46ce95666811a6c477f64697b3e526e..2cde35a666d3e78084898f57a6a2bbf561af1a4e 100644
--- a/android/src/main/java/expo/modules/kotlin/types/net/UriTypeConverter.kt
+++ b/android/src/main/java/expo/modules/kotlin/types/net/UriTypeConverter.kt
@@ -7,6 +7,7 @@ import expo.modules.kotlin.jni.CppType
 import expo.modules.kotlin.jni.ExpectedType
 import expo.modules.kotlin.types.DynamicAwareTypeConverters
 import java.net.URI
+import expo.modules.kotlin.exception.DynamicCastException
 
 class UriTypeConverter(isOptional: Boolean) : DynamicAwareTypeConverters<Uri>(isOptional) {
   override fun convertFromDynamic(value: Dynamic, context: AppContext?): Uri {
@@ -27,7 +28,7 @@ class UriTypeConverter(isOptional: Boolean) : DynamicAwareTypeConverters<Uri>(is
 class JavaURITypeConverter(isOptional: Boolean) : DynamicAwareTypeConverters<URI>(isOptional) {
   override fun convertFromDynamic(value: Dynamic, context: AppContext?): URI {
     val stringUri = value.asString()
-    return URI.create(stringUri)
+    return URI.create(stringUri) ?: throw DynamicCastException(Uri::class)
   }
 
   override fun convertFromAny(value: Any, context: AppContext?): URI {
diff --git a/expo-module-gradle-plugin/build.gradle.kts b/expo-module-gradle-plugin/build.gradle.kts
index 40a970e261a53794622e407a3faf0c84389b9991..9697cea7f2b307a92cacf8fc2f503d2198946aaa 100644
--- a/expo-module-gradle-plugin/build.gradle.kts
+++ b/expo-module-gradle-plugin/build.gradle.kts
@@ -2,7 +2,7 @@ import org.gradle.api.tasks.testing.logging.TestExceptionFormat
 import org.jetbrains.kotlin.gradle.tasks.KotlinCompile
 
 plugins {
-  kotlin("jvm") version "1.9.24"
+  kotlin("jvm") version "2.1.20"
   id("java-gradle-plugin")
 }
 
