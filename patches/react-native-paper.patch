diff --git a/src/components/Card/Card.tsx b/src/components/Card/Card.tsx
index a86e1724720da7b4fc810b41121da55ee0924c56..c64169067e5f081c06ed7150f2795d6726ae89e0 100644
--- a/src/components/Card/Card.tsx
+++ b/src/components/Card/Card.tsx
@@ -207,6 +207,9 @@ const CardComponent = (
   ]);
 
   const runElevationAnimation = (pressType: HandlePressType) => {
+    if (isV3 && isMode('contained')) {
+      return;
+    }
     const isPressTypeIn = pressType === 'in';
     if (dark && isAdaptiveMode) {
       Animated.timing(elevationDarkAdaptive, {
diff --git a/src/components/Card/CardCover.tsx b/src/components/Card/CardCover.tsx
index 337de2a94402e3e82908e4b00cffcc9cef2bd97b..e0147381dc866a4a4106780c808d89bd7e118e2f 100644
--- a/src/components/Card/CardCover.tsx
+++ b/src/components/Card/CardCover.tsx
@@ -86,7 +86,6 @@ const styles = StyleSheet.create({
     flex: 1,
     height: undefined,
     width: undefined,
-    padding: 16,
     justifyContent: 'flex-end',
   },
 });
