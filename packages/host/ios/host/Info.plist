<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>Super App Showcase</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string></string>
	<key>RCTNewArchEnabled</key>
	<true/>
	<key>RepackPublicKey</key>
	<string>-----BEGIN PUBLIC KEY-----
MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAvR2JseYMjDTie9MYo/Tw
4xGQKSBKJ15GcrwcP+eX96WWCkbTH+Oa7oUwuM2l6KyTBZsuWiTbajldnUslyfLu
uLmAfOjmgVMgWqihRIkvBIzxG2G/wrUGMLOrXWn01enqHORami0N9O7/WayC3UO1
KwN7c+Rmhkk7pwxQUDcsLofkPAjCAbo5SRDv04y0jjvjyXnufepVamSBcVdclEui
d6nrDR9vWMhvNrSW6op4/NaJM0tMqvyzwNM5p081GkqSrkS59vPkkwaFOEf2/Awg
yNsK+Efo4WDPEcZjay6S/wwhD1vw/29w/Or5KXfusIHsJu1iMC7xi5sYb9Z+pUZH
zLqv9xPF0Q040NBOSgY1dpuPm1WE3/Yd1zG1vfWFCQJ606+DVFjVODj/uTlKldxY
+vTvrfL5mZohGiYMvO4h4DLXKDlsuI0MAbCYEO9wvm/vGEP3HTpb/O85VarT4CU/
wsPsZn01/gGJmqS+2v/0428SHra0r1JOXhgOrJDn/SpWkfhcYPti61gq9qKmjylq
549qj7P7TNS0hdk8gaItciTkN6oNdvLUi3xHWjFauq2nvGq7DrNGis9agb91h6qp
cY5Xs2ruzayYogugDiaALCPvJpQFCj83AIbWft26fl7VCphxPKaFpeDHseVi6tMi
1+opwSqunAaqo5rsLlHIw0MCAwEAAQ==
-----END PUBLIC KEY-----</string>
	<key>UIAppFonts</key>
	<array>
		<string>AntDesign.ttf</string>
		<string>Entypo.ttf</string>
		<string>EvilIcons.ttf</string>
		<string>Feather.ttf</string>
		<string>FontAwesome.ttf</string>
		<string>FontAwesome5_Brands.ttf</string>
		<string>FontAwesome5_Regular.ttf</string>
		<string>FontAwesome5_Solid.ttf</string>
		<string>Foundation.ttf</string>
		<string>Ionicons.ttf</string>
		<string>MaterialIcons.ttf</string>
		<string>MaterialCommunityIcons.ttf</string>
		<string>SimpleLineIcons.ttf</string>
		<string>Octicons.ttf</string>
		<string>Zocial.ttf</string>
		<string>Fontisto.ttf</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>BootSplash</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UIRequiresFullScreen</key>
	<true/>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
