import {ScriptManager} from '@callstack/repack/client';
import {AppRegistry} from 'react-native';
import App from './src/App';
import {name as appName} from './app.json';
import {MMKV} from 'react-native-mmkv';

const storage = new MMKV({
  id: 'host-storage',
  encryptionKey: 'super-app',
});

const mmkvStorage = {
  setItem: (key, data) => storage.set(key, data),
  getItem: key => storage.getString(key) || null,
  removeItem: key => storage.delete(key),
};

// Thêm resolver động cho mini-apps
ScriptManager.shared.addResolver(async (scriptId, caller) => {
  // Cấu hình cố định cho các mini-apps mới
  const dynamicRemotes = {
    'your-mini-app': `your-mini-app@https://your-server.com/${Platform.OS}/mf-manifest.json`,
  };
  
  // Ki<PERSON>m tra nếu scriptId thuộc về mini-app mới
  const [remote] = scriptId.split('/');
  if (dynamicRemotes[remote]) {
    const url = dynamicRemotes[remote].split('@')[1];
    const manifestUrl = url;
    const response = await fetch(manifestUrl);
    const manifest = await response.json();
    
    return {
      url: manifest.url,
      query: manifest.query || {},
    };
  }
  
  return null;
});

// Only set storage caching in production
// @todo: fix this to be reliable in both dev and prod
if (!__DEV__) {
  ScriptManager.shared.setStorage(mmkvStorage);
}

AppRegistry.registerComponent(appName, () => App);
