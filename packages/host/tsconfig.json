{"compilerOptions": {"target": "esnext", "lib": ["es2017"], "jsx": "react-native", "module": "es2020", "moduleResolution": "node", "types": ["react-native", "jest"], "resolveJsonModule": true, "allowJs": true, "noEmit": true, "isolatedModules": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "skipLibCheck": true}, "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"]}