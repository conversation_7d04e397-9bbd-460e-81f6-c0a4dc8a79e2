pluginManagement {
    apply from: "../node_modules/super-app-showcase-sdk/android/resolveNodePackage.gradle"
    // [super-app-showcase change]
    // resolve react-native-gradle-plugin & CLI dynamically
    def reactNativePath = resolveNodePackage('react-native', rootDir)
    def gradlePluginPath = resolveNodePackage('@react-native/gradle-plugin', reactNativePath)
    // expose gradlePluginPath outside of pluginManagement block
    settings.ext.gradlePluginPath = gradlePluginPath.path
    includeBuild(gradlePluginPath.path)

    def expoPluginsPath = new File(
        providers.exec {
        workingDir(rootDir)
        commandLine("node", "--print", "require.resolve('expo-modules-autolinking/package.json', { paths: [require.resolve('expo/package.json')] })")
        }.standardOutput.asText.get().trim(),
        "../android/expo-gradle-plugin"
    ).absolutePath
    includeBuild(expoPluginsPath)
}

plugins { id("com.facebook.react.settings")
    id("expo-autolinking-settings")
}
extensions.configure(com.facebook.react.ReactSettingsExtension) { ex -> ex.autolinkLibrariesFromCommand(expoAutolinking.rnConfigCommand) }

rootProject.name = 'host'

include ':app'
includeBuild(ext.gradlePluginPath)
expoAutolinking.useExpoModules()
expoAutolinking.useExpoVersionCatalog()
includeBuild(expoAutolinking.reactNativeGradlePlugin)
