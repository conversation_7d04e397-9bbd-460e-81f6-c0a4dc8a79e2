import { MMKV } from "react-native-mmkv"

const storage = new MMKV({
  id: 'auth-storage',
  encryptionKey: 'super-app'
})

class AuthService {
  TOKEN_KEY = 'token';

  getCredentials(): string | undefined {
    return storage.getString(this.TOKEN_KEY);
  }

  setCredentials(token: string): void {
    return storage.set(this.TOKEN_KEY, token);
  }

  removeCredentials(): void {
    return storage.delete(this.TOKEN_KEY);
  }

  static shared = new AuthService();
}

export default AuthService;
