pluginManagement {
    apply from: "../node_modules/super-app-showcase-sdk/android/resolveNodePackage.gradle"
    // [super-app-showcase change]
    // resolve react-native-gradle-plugin & CLI dynamically
    def reactNativePath = resolveNodePackage('react-native', rootDir)
    def gradlePluginPath = resolveNodePackage('@react-native/gradle-plugin', reactNativePath)
    // expose gradlePluginPath outside of pluginManagement block
    settings.ext.gradlePluginPath = gradlePluginPath.path
    includeBuild(gradlePluginPath.path)
}

plugins { id("com.facebook.react.settings") }
extensions.configure(com.facebook.react.ReactSettingsExtension) { ex -> ex.autolinkLibrariesFromCommand() }

rootProject.name = 'dashboard'

include ':app'
includeBuild(ext.gradlePluginPath)
