import {ScriptManager} from '@callstack/repack/client';
import {AppRegistry} from 'react-native';
import App from './src/App';
import {name as appName} from './app.json';
import {MMKV} from 'react-native-mmkv';

const storage = new MMKV({
  id: 'dashboard-storage',
  encryptionKey: 'super-app',
});

const mmkvStorage = {
  setItem: (key, data) => storage.set(key, data),
  getItem: key => storage.getString(key) || null,
  removeItem: key => storage.delete(key),
};

// Only set storage caching in production
// @todo: fix this to be reliable in both dev and prod
if (!__DEV__) {
  ScriptManager.shared.setStorage(mmkvStorage);
}

AppRegistry.registerComponent(appName, () => App);
