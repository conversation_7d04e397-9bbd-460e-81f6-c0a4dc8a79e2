{"name": "shopping", "version": "0.0.1", "private": true, "scripts": {"start": "react-native start --port 9001", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "typecheck": "tsc", "bundle": "pnpm bundle:ios && pnpm bundle:android", "bundle:ios": "react-native bundle --platform ios --entry-file index.js --dev false", "bundle:android": "react-native bundle --platform android --entry-file index.js --dev false", "align-deps": "rnx-align-deps --write", "check-deps": "rnx-align-deps"}, "dependencies": {"@bottom-tabs/react-navigation": "0.9.0", "@candlefinance/faster-image": "^1.7.2", "@module-federation/enhanced": "0.13.1", "@notifee/react-native": "^9.1.8", "@react-navigation/native": "7.0.14", "@react-navigation/native-stack": "7.2.0", "@shopify/flash-list": "^1.8.3", "@shopify/react-native-skia": "^2.0.5", "@tamagui/config": "1.127.2", "@tamagui/lucide-icons": "1.127.2", "@tamagui/toast": "1.127.2", "burnt": "^0.13.0", "dayjs": "^1.11.10", "expo": "^53.0.0", "expo-blur": "~14.1.5", "expo-constants": "~17.1.6", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.9", "react": "19.1.0", "react-dom": "19.1.0", "react-native": "0.80.0", "react-native-bottom-tabs": "0.9.0", "react-native-edge-to-edge": "1.6.0", "react-native-gesture-handler": "~2.26.0", "react-native-mmkv": "3.3.0", "react-native-paper": "5.12.5", "react-native-reanimated": "4.0.0-beta.5", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "^15.12.0", "react-native-vector-icons": "10.2.0", "react-native-video": "^6.15.0", "react-native-vision-camera": "^4.7.0", "react-native-worklets": "^0.3.0", "tamagui": "1.127.2"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/preset-env": "^7.27.2", "@babel/runtime": "^7.27.6", "@callstack/repack": "5.1.2", "@callstack/repack-plugin-expo-modules": "5.1.2", "@react-native-community/cli": "19.0.0", "@react-native/babel-preset": "0.80.0", "@react-native/eslint-config": "0.80.0", "@react-native/typescript-config": "0.80.0", "@rnx-kit/align-deps": "^2.5.1", "@rspack/core": "^1.3.15", "@swc/helpers": "^0.5.17", "@tamagui/babel-plugin": "1.127.2", "@testing-library/react-native": "^13.2.0", "@types/jest": "^29.5.14", "@types/react": "19.1.0", "@types/react-native-vector-icons": "^6.4.12", "@types/react-test-renderer": "^19.1.0", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "eslint": "^8.56.0", "jest": "^29.6.3", "prettier": "^2.8.8", "react-test-renderer": "^19.0.0", "super-app-showcase-sdk": "0.0.2", "typescript": "~5.8.3"}, "rnx-kit": {"kitType": "app", "alignDeps": {"presets": ["./node_modules/super-app-showcase-sdk/preset"], "requirements": ["super-app-showcase-sdk@0.0.2"], "capabilities": ["super-app"]}}}