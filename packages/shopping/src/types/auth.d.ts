declare module 'auth/AuthProvider' {
  import React from 'react';
  
  interface AuthData {
    isSignout: boolean;
    isLoading: boolean;
  }
  
  interface AuthProviderProps {
    children: (authData: AuthData) => React.ReactNode;
  }
  
  const AuthProvider: React.ComponentType<AuthProviderProps>;
  export default AuthProvider;
}

declare module 'auth/SignInScreen' {
  import React from 'react';
  
  const SignInScreen: React.ComponentType;
  export default SignInScreen;
}

declare module 'auth/AccountScreen' {
  import React from 'react';
  
  const AccountScreen: React.ComponentType;
  export default AccountScreen;
}
