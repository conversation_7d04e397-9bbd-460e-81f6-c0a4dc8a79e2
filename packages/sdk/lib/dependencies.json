{"react": {"name": "react", "version": "19.1.0"}, "react-dom": {"name": "react-dom", "version": "19.1.0"}, "react-native": {"name": "react-native", "version": "0.80.0"}, "@bottom-tabs/react-navigation": {"name": "@bottom-tabs/react-navigation", "version": "0.9.0"}, "@react-navigation/native": {"name": "@react-navigation/native", "version": "7.0.14"}, "@react-navigation/native-stack": {"name": "@react-navigation/native-stack", "version": "7.2.0"}, "react-native-bottom-tabs": {"name": "react-native-bottom-tabs", "version": "0.9.0"}, "react-native-paper": {"name": "react-native-paper", "version": "5.12.5"}, "react-native-safe-area-context": {"name": "react-native-safe-area-context", "version": "5.4.0"}, "react-native-screens": {"name": "react-native-screens", "version": "~4.11.1"}, "react-native-vector-icons": {"name": "react-native-vector-icons", "version": "10.2.0"}, "react-native-mmkv": {"name": "react-native-mmkv", "version": "3.3.0"}, "@module-federation/enhanced": {"name": "@module-federation/enhanced", "version": "0.13.1", "shared": false}, "expo": {"name": "expo", "version": "^53.0.0"}, "dayjs": {"name": "dayjs", "version": "^1.11.10"}, "react-native-edge-to-edge": {"name": "react-native-edge-to-edge", "version": "1.6.0"}, "react-native-gesture-handler": {"name": "react-native-gesture-handler", "version": "~2.26.0"}, "react-native-reanimated": {"name": "react-native-reanimated", "version": "4.0.0-beta.5"}, "expo-blur": {"name": "expo-blur", "version": "~14.1.5"}, "expo-constants": {"name": "expo-constants", "version": "~17.1.6"}, "expo-font": {"name": "expo-font", "version": "~13.3.1"}, "expo-haptics": {"name": "expo-haptics", "version": "~14.1.4"}, "expo-status-bar": {"name": "expo-status-bar", "version": "~2.2.3"}, "expo-symbols": {"name": "expo-symbols", "version": "~0.4.5"}, "expo-system-ui": {"name": "expo-system-ui", "version": "~5.0.9"}, "react-native-vision-camera": {"name": "react-native-vision-camera", "version": "^4.7.0"}, "react-native-video": {"name": "react-native-video", "version": "^6.15.0"}, "@candlefinance/faster-image": {"name": "@candlefinance/faster-image", "version": "^1.7.2"}, "@shopify/flash-list": {"name": "@shopify/flash-list", "version": "^1.8.3"}, "@shopify/react-native-skia": {"name": "@shopify/react-native-skia", "version": "^2.0.5"}, "@tamagui/config": {"name": "@tamagui/config", "version": "1.127.2"}, "@tamagui/lucide-icons": {"name": "@tamagui/lucide-icons", "version": "1.127.2"}, "@tamagui/toast": {"name": "@tamagui/toast", "version": "1.127.2"}, "tamagui": {"name": "tamagui", "version": "1.127.2"}, "react-native-svg": {"name": "react-native-svg", "version": "^15.12.0"}, "burnt": {"name": "burnt", "version": "^0.13.0"}, "@notifee/react-native": {"name": "@notifee/react-native", "version": "^9.1.8"}, "react-native-worklets": {"name": "react-native-worklets", "version": "^0.3.0"}}